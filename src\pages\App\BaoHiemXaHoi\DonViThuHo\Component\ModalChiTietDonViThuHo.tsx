import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal, ModalQuanLyFileCaNhan, Popcomfirm} from "@src/components";
import {Col, Flex, Form, Modal, Row} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useRef, useState} from "react";
import {FormTaoMoiDonViThuHo, TRANG_THAI_TAO_MOI_DON_VI_THU_HO} from "../index.configs";
import {useQuanLyDonViThuHoContext} from "../index.context";
import {ChiTietDonViThuHoProps, IModalChiTietDonViThuHoRef} from "./Constant";
import "../index.default.scss";
const {ma, ten, ten_tat, ten_e, dchi, dthoai, mst, stt, trang_thai, ma_nh, so_tk, ten_tk} = FormTaoMoiDonViThuHo;

const ModalChiTietDonViThuHoComponent = forwardRef<IModalChiTietDonViThuHoRef, ChiTietDonViThuHoProps>(({listDonViThuHo}: ChiTietDonViThuHoProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataDonViThuHo?: CommonExecute.Execute.IDonViThuHo) => {
      setIsOpen(true);
      if (dataDonViThuHo) setChiTietDonViThuHo(dataDonViThuHo); // nếu có dữ liệu -> set chi tiết DonViThuHo -> là sửa
    },
    close: () => setIsOpen(false),
  }));
  const [chiTietDonViThuHo, setChiTietDonViThuHo] = useState<CommonExecute.Execute.IDonViThuHo | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const {capNhatChiTietDonViThuHo, getListDonViThuHo, loading, listNganHang} = useQuanLyDonViThuHoContext();
  const [form] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const formValues = Form.useWatch([], form);
  const modalQuanLyFileRef = useRef<any>(null);
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  // init form data - load dữ liệu vào form khi sửa
  useEffect(() => {
    if (chiTietDonViThuHo) {
      const arrFormData = [];
      for (const key in chiTietDonViThuHo) {
        arrFormData.push({
          name: key,
          value: chiTietDonViThuHo[key as keyof CommonExecute.Execute.IDonViThuHo],
        });
      }
      form.setFields(arrFormData);
    }
  }, [chiTietDonViThuHo]);

  //xử lý validate form
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setChiTietDonViThuHo(null);
    form.resetFields();
  }, []);

  //Bấm Update
  const onConfirm = async () => {
    try {
      const values: ReactQuery.ICapNhatDonViThuHoParams = form.getFieldsValue(); //lấy ra values của form

      // Xác định đây là EDIT hay CREATE mode
      const isEditMode = !!chiTietDonViThuHo;

      await capNhatChiTietDonViThuHo(values, isEditMode); //cập nhật lại DonViThuHo
      await getListDonViThuHo(); //lấy lại danh sách DonViThuHo
      closeModal();
    } catch (error) {
      console.log("onConfirm", error);
    }
  };
  const handleChonFile = useCallback(() => {
    if (modalQuanLyFileRef.current?.open) {
      modalQuanLyFileRef.current.open();
    }
  }, []);
  // RENDER
  //FOOTER
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => closeModal()} className="mr-2 w-40" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Popcomfirm
          title={disableSubmit ? "Vui lòng nhập đầy đủ thông tin" : ""}
          onConfirm={onConfirm}
          okText="Lưu"
          description="Bạn có chắc muốn lưu thông tin?"
          buttonTitle={"Lưu"}
          buttonDisable={disableSubmit}
          buttonClassName="w-40"
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        />
      </Form.Item>
    );
  };
  const renderFormColum = (props?: any, span = 4) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const renderChonFile = () => {
    return (
      <div>
        {/* Ô vuông nhấn chọn */}
        <div
          onClick={handleChonFile}
          style={{
            width: 150,
            height: 150,
            border: "2px dashed #d9d9d9",
            borderRadius: 8,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            cursor: "pointer",
            overflow: "hidden",
            background: "#fafafa",
          }}>
          {imageUrl ? <img src={imageUrl} alt="selected" style={{width: "100%", height: "100%", objectFit: "cover"}} /> : <span style={{color: "#999"}}>+</span>}
        </div>
      </div>
    );
  };
  /**
   * Xử lý khi chọn file từ modal
   */
  const handleChonFileSuccess = useCallback(
    (filesSelected: any[]) => {
      console.log("handleChonFileSuccess", filesSelected);
      if (filesSelected && filesSelected.length > 0) {
        const selectedFile = filesSelected[0]; // Chỉ lấy file đầu tiên

        // Lấy id_file từ selectedFile - field 'id' trong File.GetFolder.IGetFolder
        const fileId = selectedFile.id;
        const fileName = selectedFile.ten_alias || selectedFile.ten || "File không tên";

        if (fileId) {
          // Set giá trị id_file vào form
          form.setFieldValue("id_file", Number(fileId));

          // Lưu thông tin file để hiển thị
          // setSelectedFileInfo({
          //   id: Number(fileId),
          //   name: fileName,
          // });

          // Đóng modal sau khi set thành công
          if (modalQuanLyFileRef.current?.close) {
            modalQuanLyFileRef.current.close();
          }
        } else {
          console.warn("Không tìm thấy id file trong selectedFile:", selectedFile);
        }
      }
    },
    [form],
  );
  const renderForm = () => (
    <Form form={form} layout="vertical">
      <Row gutter={16}>
        {renderFormColum({...ma, disabled: chiTietDonViThuHo ? true : false})}
        {renderFormColum({...ten}, 10)}
        {renderFormColum({...ten_tat}, 10)}
      </Row>
      <Row gutter={16}>
        {renderFormColum({...dchi}, 16)}
        {renderFormColum({...dthoai}, 8)}
      </Row>
      <Row gutter={16}>
        {renderFormColum({...ten_e}, 8)}
        {renderFormColum({...mst}, 6)}
        {renderFormColum({...stt})}
        {renderFormColum({...trang_thai, options: TRANG_THAI_TAO_MOI_DON_VI_THU_HO}, 6)}
      </Row>
      <div className="section-wrapper">
        <div className="section-title">Cấu hình tài khoản thu hộ</div>
        <Row gutter={16}>
          {renderFormColum({...ma_nh, options: listNganHang}, 8)}
          {renderFormColum({...so_tk}, 8)}
          {renderFormColum({...ten_tk}, 8)}
        </Row>
        {/* ô chọn ảnh để mở lên modalfile */}
        {/* <Button type="primary" icon={<FolderOpenOutlined />} style={{width: "40px"}} onClick={handleChonFile} title="Chọn file từ hệ thống" /> */}
        {renderChonFile()}
      </div>
    </Form>
  );
  //Render
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={
          <HeaderModal
            title={chiTietDonViThuHo ? `${chiTietDonViThuHo.ten}` : "Tạo mới đơn vị thu hộ bhxh"}
            trang_thai_ten={chiTietDonViThuHo?.trang_thai_ten}
            trang_thai={chiTietDonViThuHo?.trang_thai}
          />
        }
        // centered
        className="modal-chi-tiet-don-vi-thu-ho"
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={800}
        style={{
          top: 30,
        }}
        styles={
          {
            // body: {
            //   paddingTop: "8px",
            //   paddingBottom: "16px",
            // },
          }
        }
        footer={renderFooter}>
        {renderForm()}
        <ModalQuanLyFileCaNhan ref={modalQuanLyFileRef} onClickChonFile={handleChonFileSuccess} />
      </Modal>
    </Flex>
  );
});

ModalChiTietDonViThuHoComponent.displayName = "ModalChiTietDonViThuHoComponent";
export const ModalChiTietDonViThuHo = memo(ModalChiTietDonViThuHoComponent, isEqual);
